#!/usr/bin/env python3
"""
测试改进后的 LightGBM 趋势预测能力
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加 woo-utils 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'woo-utils'))

from woo_utils.predict import LightGBM_predict

def test_linear_trend():
    """测试线性趋势预测"""
    print("测试 1: 线性趋势预测 (1,2,3,...,30)")
    
    # 创建简单的线性序列
    dates = pd.date_range('2023-01-01', periods=30, freq='D')
    values = list(range(1, 31))
    
    df = pd.DataFrame({
        'date': dates.strftime('%Y-%m-%d'),
        'value': values
    })
    
    print(f"输入: {values[:5]} ... {values[-5:]}")
    print(f"期望预测: [31, 32, 33, 34, 35]")
    
    # 使用改进的参数进行预测
    result = LightGBM_predict(
        df, 
        n=5,
        sequence_length=8,  # 使用较短的序列长度
        num_boost_round=100,  # 增加训练轮数
        learning_rate=0.1,
        num_leaves=15,  # 减少叶子节点数
        verbose=-1
    )
    
    if result is not None:
        predicted_values = result['predicted_mean'].values
        print(f"实际预测: {predicted_values.round(2)}")
        
        # 计算与期望值的差异
        expected = [31, 32, 33, 34, 35]
        diff = np.abs(predicted_values - expected)
        avg_diff = np.mean(diff)
        max_diff = np.max(diff)
        
        print(f"平均误差: {avg_diff:.3f}")
        print(f"最大误差: {max_diff:.3f}")
        
        # 检查趋势方向
        is_increasing = all(predicted_values[i] <= predicted_values[i+1] for i in range(len(predicted_values)-1))
        print(f"趋势递增: {is_increasing}")
        
        if avg_diff < 2.0 and is_increasing:
            print("✅ 线性趋势预测良好")
            return True
        else:
            print("❌ 线性趋势预测需要改进")
            return False
    else:
        print("❌ 预测失败")
        return False

def test_steep_trend():
    """测试陡峭趋势预测"""
    print("\n测试 2: 陡峭线性趋势预测")
    
    # 创建陡峭的线性序列
    dates = pd.date_range('2023-01-01', periods=20, freq='D')
    values = [i * 5 for i in range(1, 21)]  # 5, 10, 15, ..., 100
    
    df = pd.DataFrame({
        'date': dates.strftime('%Y-%m-%d'),
        'value': values
    })
    
    print(f"输入: {values[:3]} ... {values[-3:]}")
    print(f"期望预测: [105, 110, 115]")
    
    result = LightGBM_predict(
        df, 
        n=3,
        sequence_length=6,
        num_boost_round=80,
        learning_rate=0.1,
        num_leaves=20,
        verbose=-1
    )
    
    if result is not None:
        predicted_values = result['predicted_mean'].values
        print(f"实际预测: {predicted_values.round(1)}")
        
        expected = [105, 110, 115]
        diff = np.abs(predicted_values - expected)
        avg_diff = np.mean(diff)
        
        print(f"平均误差: {avg_diff:.1f}")
        
        if avg_diff < 10.0:
            print("✅ 陡峭趋势预测良好")
            return True
        else:
            print("❌ 陡峭趋势预测需要改进")
            return False
    else:
        print("❌ 预测失败")
        return False

def test_noisy_trend():
    """测试带噪声的趋势预测"""
    print("\n测试 3: 带噪声的线性趋势预测")
    
    # 创建带噪声的线性序列
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=40, freq='D')
    base_values = np.arange(1, 41)
    noise = np.random.normal(0, 1, 40)  # 添加噪声
    values = base_values + noise
    
    df = pd.DataFrame({
        'date': dates.strftime('%Y-%m-%d'),
        'value': values
    })
    
    print(f"输入: 线性趋势 + 噪声，最后5个值: {values[-5:].round(2)}")
    
    result = LightGBM_predict(
        df, 
        n=5,
        sequence_length=10,
        num_boost_round=60,
        learning_rate=0.08,
        num_leaves=25,
        verbose=-1
    )
    
    if result is not None:
        predicted_values = result['predicted_mean'].values
        print(f"预测结果: {predicted_values.round(2)}")
        
        # 检查趋势方向（应该大致递增）
        trend_slope = np.polyfit(range(len(predicted_values)), predicted_values, 1)[0]
        print(f"预测趋势斜率: {trend_slope:.2f}")
        
        if trend_slope > 0.5:  # 期望斜率接近1
            print("✅ 带噪声趋势预测良好")
            return True
        else:
            print("❌ 带噪声趋势预测需要改进")
            return False
    else:
        print("❌ 预测失败")
        return False

def main():
    """主函数"""
    print("LightGBM 趋势预测能力测试")
    print("目标: 让 LightGBM 自身学会预测趋势，而不依赖特殊的线性回归分支")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    if test_linear_trend():
        success_count += 1
    
    if test_steep_trend():
        success_count += 1
        
    if test_noisy_trend():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"测试结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("✅ LightGBM 趋势预测能力优秀！")
    elif success_count >= 2:
        print("⚠️ LightGBM 趋势预测能力良好，还有改进空间")
    else:
        print("❌ LightGBM 趋势预测能力需要进一步改进")

if __name__ == "__main__":
    main()
