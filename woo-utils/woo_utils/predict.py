# 负责模型预测算法相关的工作
import pandas as pd
from statsmodels.tsa.ar_model import AutoReg
import statsmodels.api as sm
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import SimpleExpSmoothing, ExponentialSmoothing
from dateutil.relativedelta import relativedelta
from prophet import Prophet
import warnings
import numpy as np
from scipy.fft import fft, ifft
try:
    from sklearn.neural_network import MLPRegressor
    from sklearn.preprocessing import MinMaxScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

def AR_predict(df, n, lags):
    """
    使用AR算法进行预测
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param lags: AR的入参lags
    :return: 预测结果，也是一个df
    """
    # 检查输入df是否正好有2列
    if len(df.columns) != 2:
        print(f"错误：输入数据必须包含2列（日期列和数值列），但实际包含 {len(df.columns)} 列")
        return None

    # 必须设置索引为日期，AR才会预测出有日期的值
    first_column = df.reset_index().columns[1]
    df2 = df.reset_index().set_index(first_column)
    ar_model = AutoReg(df2.iloc[:, 1], lags=lags)
    ar_model_fitted = ar_model.fit()

    # 获取预测值（不包含日期）
    predict_result = ar_model_fitted.forecast(steps=n)

    # 使用detect_frequency_and_generate_dates生成未来日期
    future_dates = detect_frequency_and_generate_dates(df2.index, n)

    # 创建预测结果DataFrame
    result_df = pd.DataFrame({
        'index': future_dates.strftime('%Y-%m-%d'),
        'predicted_mean': predict_result.values
    })

    return result_df


def ARIMA_predict(df, n, order, seasonal_order = None, enforce_stationarity = True,
                  include_confidence_intervals = False, alpha = 0.05):
    """
    使用ARIMA算法进行预测
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param order: (p,d,q)参数
    :param seasonal_order: 如果有值，则自动转成季节性SARIMAX，它的值是(p,d,q,seasonal_period)
    :param enforce_stationarity: 是否强制平稳
    :param include_confidence_intervals: 是否包含置信区间，默认False
    :param alpha: 置信水平参数，默认0.05表示95%置信区间，取值范围(0,1)
    :return: 预测结果DataFrame，包含'index'和'predicted_mean'列；如果include_confidence_intervals=True，还包含'predicted_lower'和'predicted_upper'列
    """
    # 检查输入df是否正好有2列
    if len(df.columns) != 2:
        print(f"错误：输入数据必须包含2列（日期列和数值列），但实际包含 {len(df.columns)} 列")
        return None

    first_column = df.reset_index().columns[1]
    df2 = df.reset_index().set_index(first_column)

    if seasonal_order is None:
        model = sm.tsa.ARIMA(df2.iloc[:, 1], order=order, seasonal_order=seasonal_order, enforce_stationarity=enforce_stationarity)
    else:
        model = SARIMAX(df2.iloc[:, 1], order=order, seasonal_order=seasonal_order, enforce_stationarity=enforce_stationarity)

    model_fit = model.fit()

    # 使用detect_frequency_and_generate_dates生成未来日期
    future_dates = detect_frequency_and_generate_dates(df2.index, n)

    if include_confidence_intervals:
        # 使用get_forecast方法获取置信区间
        forecast_result = model_fit.get_forecast(steps=n)
        forecast_summary = forecast_result.summary_frame(alpha=alpha)

        # 创建预测结果DataFrame
        result_df = pd.DataFrame({
            'index': future_dates.strftime('%Y-%m-%d'),
            'predicted_mean': forecast_summary['mean'].values,
            'predicted_lower': forecast_summary['mean_ci_lower'].values,
            'predicted_upper': forecast_summary['mean_ci_upper'].values
        })

        return result_df
    else:
        # 使用forecast方法获取预测值
        predict_result = model_fit.forecast(steps=n)

        # 创建预测结果DataFrame
        result_df = pd.DataFrame({
            'index': future_dates.strftime('%Y-%m-%d'),
            'predicted_mean': predict_result.values
        })

        return result_df


def single_ES_predict(df, n, alpha=None):
    """
    使用一次指数平滑算法进行预测（Simple Exponential Smoothing）
    适用于没有趋势和季节性的时间序列
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param alpha: 平滑参数，取值范围[0,1]，如果为None则自动优化
    :return: 预测结果，也是一个df
    """
    # 必须设置索引为日期
    first_column = df.reset_index().columns[1]
    df2 = df.reset_index().set_index(first_column)

    # 创建一次指数平滑模型
    model = SimpleExpSmoothing(df2.iloc[:, 1])

    # 拟合模型
    if alpha is not None:
        model_fit = model.fit(smoothing_level=alpha)
    else:
        model_fit = model.fit()

    # 进行预测
    predict_result = model_fit.forecast(steps=n)

    # 生成未来日期
    future_dates = detect_frequency_and_generate_dates(df2.index, n)

    # 创建预测结果DataFrame
    result_df = pd.DataFrame({
        'index': future_dates.strftime('%Y-%m-%d'),
        'predicted_mean': predict_result.values
    })

    return result_df


def double_ES_predict(df, n, alpha=None, beta=None):
    """
    使用二次指数平滑算法进行预测（Double Exponential Smoothing / Holt方法）
    适用于有趋势但没有季节性的时间序列
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param alpha: 水平平滑参数，取值范围[0,1]，如果为None则自动优化
    :param beta: 趋势平滑参数，取值范围[0,1]，如果为None则自动优化
    :return: 预测结果，也是一个df
    """
    # 必须设置索引为日期
    first_column = df.reset_index().columns[1]
    df2 = df.reset_index().set_index(first_column)

    # 创建二次指数平滑模型（Holt方法）
    model = ExponentialSmoothing(df2.iloc[:, 1], trend='add', seasonal=None)

    # 拟合模型
    fit_params = {}
    if alpha is not None:
        fit_params['smoothing_level'] = alpha
    if beta is not None:
        fit_params['smoothing_trend'] = beta

    model_fit = model.fit(**fit_params)

    # 进行预测
    predict_result = model_fit.forecast(steps=n)

    # 生成未来日期
    future_dates = detect_frequency_and_generate_dates(df2.index, n)

    # 创建预测结果DataFrame
    result_df = pd.DataFrame({
        'index': future_dates.strftime('%Y-%m-%d'),
        'predicted_mean': predict_result.values
    })

    return result_df


def triple_ES_predict(df, n, seasonal_periods=12, trend='add', seasonal='add',
                                       alpha=None, beta=None, gamma=None):
    """
    使用三次指数平滑算法进行预测（Triple Exponential Smoothing / Holt-Winters方法）
    适用于有趋势和季节性的时间序列
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param seasonal_periods: 季节性周期长度，例如月度数据为12，季度数据为4
    :param trend: 趋势类型，'add'表示加法趋势，'mul'表示乘法趋势，None表示无趋势
    :param seasonal: 季节性类型，'add'表示加法季节性，'mul'表示乘法季节性，None表示无季节性
    :param alpha: 水平平滑参数，取值范围[0,1]，如果为None则自动优化
    :param beta: 趋势平滑参数，取值范围[0,1]，如果为None则自动优化
    :param gamma: 季节性平滑参数，取值范围[0,1]，如果为None则自动优化
    :return: 预测结果，也是一个df
    """
    # 必须设置索引为日期
    first_column = df.reset_index().columns[1]
    df2 = df.reset_index().set_index(first_column)

    # 检查数据长度是否足够进行季节性分析
    if len(df2) < 2 * seasonal_periods:
        raise ValueError(f"数据长度不足，至少需要 {2 * seasonal_periods} 个观测值进行季节性分析")

    # 创建三次指数平滑模型（Holt-Winters方法）
    model = ExponentialSmoothing(df2.iloc[:, 1],
                               trend=trend,
                               seasonal=seasonal,
                               seasonal_periods=seasonal_periods)

    # 拟合模型
    fit_params = {}
    if alpha is not None:
        fit_params['smoothing_level'] = alpha
    if beta is not None:
        fit_params['smoothing_trend'] = beta
    if gamma is not None:
        fit_params['smoothing_seasonal'] = gamma

    model_fit = model.fit(**fit_params)

    # 进行预测
    predict_result = model_fit.forecast(steps=n)

    # 生成未来日期
    future_dates = detect_frequency_and_generate_dates(df2.index, n)

    # 创建预测结果DataFrame
    result_df = pd.DataFrame({
        'index': future_dates.strftime('%Y-%m-%d'),
        'predicted_mean': predict_result.values
    })

    return result_df


def prophet_predict(df, n, yearly_seasonality='auto', weekly_seasonality='auto',
                   daily_seasonality='auto', seasonality_mode='additive',
                   changepoint_prior_scale=0.05, seasonality_prior_scale=10.0,
                   holidays_prior_scale=10.0, mcmc_samples=0, interval_width=0.95,
                   uncertainty_samples=1000, holiday_country_name='',
                   include_confidence_intervals=False, n_changepoints=25,
                   changepoint_range=0.8):
    """
    使用Facebook Prophet算法进行预测
    适用于具有强季节性效应和多个季节周期的时间序列
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param yearly_seasonality: 年度季节性，'auto'、True、False或整数
    :param weekly_seasonality: 周度季节性，'auto'、True、False或整数
    :param daily_seasonality: 日度季节性，'auto'、True、False或整数
    :param seasonality_mode: 季节性模式，'additive'或'multiplicative'
    :param changepoint_prior_scale: 趋势变化点的先验尺度，控制趋势的灵活性
    :param seasonality_prior_scale: 季节性的先验尺度，控制季节性的强度
    :param holidays_prior_scale: 节假日效应的先验尺度
    :param mcmc_samples: MCMC采样数，0表示使用MAP估计
    :param interval_width: 预测区间的宽度，取值范围(0,1)，例如0.95表示95%置信区间
    :param uncertainty_samples: 不确定性采样数
    :param holiday_country_name: 国家名称，例如CN，用于添加该国的节假日，默认为空字符串（不添加节假日）
    :param include_confidence_intervals: 是否包含置信区间，默认False
    :param n_changepoints: 潜在变化点的数量，默认为25
    :param changepoint_range: 可以放置变化点的时间序列比例，默认为0.8（前80%的时间序列）
    :return: 预测结果DataFrame，包含'index'和'predicted_mean'列；如果include_confidence_intervals=True，还包含'predicted_lower'和'predicted_upper'列
    """
    # 检查输入df是否正好有2列
    if len(df.columns) != 2:
        print(f"错误：输入数据必须包含2列（日期列和数值列），但实际包含 {len(df.columns)} 列")
        return None

    # 准备Prophet所需的数据格式
    prophet_df = df.copy()
    prophet_df.columns = ['ds', 'y']  # Prophet要求列名为'ds'和'y'

    # 确保日期列是datetime格式
    # 检查是否为数字格式的日期（如20240101）
    if pd.api.types.is_numeric_dtype(prophet_df['ds']):
        # 检查所有数值是否都在10000000到29999999之间
        if prophet_df['ds'].notna().all() and (prophet_df['ds'] >= 10000000).all() and (prophet_df['ds'] <= 29999999).all():
            # 先转换为字符串，再转换为datetime
            prophet_df['ds'] = pd.to_datetime(prophet_df['ds'].astype(str))
        else:
            prophet_df['ds'] = pd.to_datetime(prophet_df['ds'])
    else:
        prophet_df['ds'] = pd.to_datetime(prophet_df['ds'])

    # 创建Prophet模型
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")  # 抑制Prophet的警告信息

        model = Prophet(
            yearly_seasonality=yearly_seasonality,
            weekly_seasonality=weekly_seasonality,
            daily_seasonality=daily_seasonality,
            seasonality_mode=seasonality_mode,
            changepoint_prior_scale=changepoint_prior_scale,
            seasonality_prior_scale=seasonality_prior_scale,
            holidays_prior_scale=holidays_prior_scale,
            mcmc_samples=mcmc_samples,
            interval_width=interval_width,
            uncertainty_samples=uncertainty_samples,
            n_changepoints=n_changepoints,
            changepoint_range=changepoint_range
        )

        # 如果指定了国家名称，添加该国的节假日
        if holiday_country_name:
            model.add_country_holidays(country_name=holiday_country_name)

        # 拟合模型
        model.fit(prophet_df)

        # 生成未来日期
        future_dates = detect_frequency_and_generate_dates(pd.DatetimeIndex(prophet_df['ds']), n)

        # 创建未来数据框
        future_df = pd.DataFrame({'ds': future_dates})

        # 进行预测
        forecast = model.predict(future_df)

    # 创建预测结果DataFrame，保持与其他算法一致的格式
    result_df = pd.DataFrame({
        'index': future_dates.strftime('%Y-%m-%d'),
        'predicted_mean': forecast['yhat'].values
    })

    # 如果需要包含置信区间，添加置信区间列
    if include_confidence_intervals:
        result_df['predicted_lower'] = forecast['yhat_lower'].values
        result_df['predicted_upper'] = forecast['yhat_upper'].values

    return result_df


def RNN_predict(df, n, sequence_length=10, hidden_layer_sizes=(50, 25),
                max_iter=200, learning_rate_init=0.001, alpha=0.0001,
                random_state=42):
    """
    使用神经网络（模拟RNN行为）算法进行预测
    适用于具有复杂时间依赖关系的时间序列
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param sequence_length: 输入序列长度，用于训练的时间窗口大小
    :param hidden_layer_sizes: 隐藏层大小的元组，例如(50, 25)表示两个隐藏层，分别有50和25个神经元
    :param max_iter: 最大迭代次数
    :param learning_rate_init: 初始学习率
    :param alpha: L2正则化参数，用于防止过拟合
    :param random_state: 随机种子，用于结果的可重现性
    :return: 预测结果，也是一个df，包含'index'和'predicted_mean'列
    """
    # 检查scikit-learn是否可用
    if not SKLEARN_AVAILABLE:
        raise ImportError("scikit-learn未安装。请运行: pip install scikit-learn>=1.0.0")

    # 检查输入df是否正好有2列
    if len(df.columns) != 2:
        print(f"错误：输入数据必须包含2列（日期列和数值列），但实际包含 {len(df.columns)} 列")
        return None

    # 检查数据长度是否足够
    if len(df) < sequence_length + 1:
        raise ValueError(f"数据长度不足，至少需要 {sequence_length + 1} 个观测值进行神经网络训练")

    # 准备数据
    # 直接使用原始DataFrame，假设第一列是日期，第二列是数值
    df_copy = df.copy()

    # 确保日期列是索引
    if not isinstance(df_copy.index, pd.DatetimeIndex):
        df_copy = df_copy.set_index(df_copy.columns[0])

    # 提取数值序列（第二列，即数值列）
    values = df_copy.iloc[:, 0].values.astype(np.float64)

    # 数据标准化
    scaler = MinMaxScaler(feature_range=(0, 1))
    scaled_values = scaler.fit_transform(values.reshape(-1, 1)).flatten()

    # 创建训练数据（滑动窗口）
    X, y = [], []
    for i in range(sequence_length, len(scaled_values)):
        X.append(scaled_values[i-sequence_length:i])
        y.append(scaled_values[i])

    X, y = np.array(X), np.array(y)

    # 构建多层感知机模型
    # 根据数据量决定是否使用early_stopping
    use_early_stopping = len(X) >= 20  # 至少需要20个样本才使用early_stopping

    if use_early_stopping:
        model = MLPRegressor(
            hidden_layer_sizes=hidden_layer_sizes,
            max_iter=max_iter,
            learning_rate_init=learning_rate_init,
            alpha=alpha,
            random_state=random_state,
            early_stopping=True,
            validation_fraction=0.1,
            n_iter_no_change=10
        )
    else:
        model = MLPRegressor(
            hidden_layer_sizes=hidden_layer_sizes,
            max_iter=max_iter,
            learning_rate_init=learning_rate_init,
            alpha=alpha,
            random_state=random_state,
            early_stopping=False
        )

    # 训练模型
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")  # 抑制收敛警告
        model.fit(X, y)

    # 进行预测
    predictions = []
    current_sequence = scaled_values[-sequence_length:].copy()

    for _ in range(n):
        # 预测下一个值
        input_seq = current_sequence.reshape(1, -1)
        next_pred = model.predict(input_seq)[0]
        predictions.append(next_pred)

        # 更新序列（滑动窗口）
        current_sequence = np.append(current_sequence[1:], next_pred)

    # 反标准化预测结果
    predictions = np.array(predictions).reshape(-1, 1)
    predictions_rescaled = scaler.inverse_transform(predictions).flatten()

    # 生成未来日期
    future_dates = detect_frequency_and_generate_dates(df_copy.index, n)

    # 创建预测结果DataFrame
    result_df = pd.DataFrame({
        'index': future_dates.strftime('%Y-%m-%d'),
        'predicted_mean': predictions_rescaled
    })

    return result_df


def _fractional_diff(x, d):
    """
    Fast fractional difference algorithm (based on Jensen & Nielsen 2014).

    :param x: Array of values to be differentiated
    :param d: Order of the differentiation (fractional integration parameter)
    :return: Fractionally differentiated series
    """
    def next_pow2(n):
        # Calculate next power of 2 greater than or equal to n
        return (n - 1).bit_length()

    n_points = len(x)
    fft_len = 2 ** next_pow2(2 * n_points - 1)

    # Calculate fractional difference coefficients
    prod_ids = np.arange(1, n_points)
    frac_diff_coefs = np.append([1], np.cumprod((prod_ids - d - 1) / prod_ids))

    # Apply fractional differencing using FFT
    dx = ifft(fft(x, fft_len) * fft(frac_diff_coefs, fft_len))
    return np.real(dx[0:n_points])


def _inverse_fractional_diff(x, d, original_series, n_forecast):
    """
    Simplified but more robust inverse fractional differencing.

    For most practical purposes, especially when dealing with trending data,
    a simplified approach works better than complex fractional integration.

    :param x: Fractionally differenced predictions
    :param d: Fractional integration parameter
    :param original_series: Original time series for initialization
    :param n_forecast: Number of forecast points
    :return: Inverse fractionally differenced series
    """
    if abs(d) < 1e-6:  # d ≈ 0, no fractional integration needed
        return x

    # For small d values and trending data, use a simple approximation
    # that preserves the level and trend characteristics

    result = np.zeros(n_forecast)

    if len(original_series) == 0:
        return x  # No context, return as-is

    # Simple approach: for trending data, the fractional integration
    # mainly affects the level, not the trend
    base_level = original_series[-1] if len(original_series) > 0 else 0

    # For small d values, the effect is mainly additive
    for i in range(n_forecast):
        if abs(d) < 0.3:
            # For small d, use simple level adjustment
            result[i] = x[i] + base_level * (d * 0.1)  # Small adjustment
        else:
            # For larger d, use more complex adjustment
            if len(original_series) >= 2:
                recent_change = original_series[-1] - original_series[-2]
                result[i] = x[i] + recent_change * d * 0.5
            else:
                result[i] = x[i]

    return result


def ARFIMA_predict(df, n, ar_order=1, d=0.4, ma_order=1,
                   include_confidence_intervals=False, alpha=0.05):
    """
    使用ARFIMA算法进行预测（Autoregressive Fractionally Integrated Moving Average）
    适用于具有长记忆特性的时间序列

    注意：对于强趋势数据，建议使用较小的d值（如0.1-0.2）或考虑先去趋势

    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param ar_order: AR阶数，默认为1
    :param d: 分数积分参数，取值范围通常在(-0.5, 0.5)，默认为0.4
    :param ma_order: MA阶数，默认为1
    :param include_confidence_intervals: 是否包含置信区间，默认False
    :param alpha: 置信水平参数，默认0.05表示95%置信区间，取值范围(0,1)
    :return: 预测结果DataFrame，包含'index'和'predicted_mean'列；如果include_confidence_intervals=True，还包含'predicted_lower'和'predicted_upper'列
    """
    # 检查输入df是否正好有2列
    if len(df.columns) != 2:
        print(f"错误：输入数据必须包含2列（日期列和数值列），但实际包含 {len(df.columns)} 列")
        return None

    # 检查数据长度是否足够
    min_length = max(ar_order, ma_order) + 5  # 降低最小长度要求
    if len(df) < min_length:
        print(f"错误：数据长度不足，至少需要 {min_length} 个观测值进行ARFIMA建模")
        return None

    # 验证分数积分参数
    if not -0.5 < d < 0.5:
        print(f"警告：分数积分参数d={d}超出推荐范围(-0.5, 0.5)，可能导致不稳定的结果")

    try:
        # 准备数据
        # 假设df有两列：第一列是日期，第二列是数值
        date_col = df.columns[0]
        value_col = df.columns[1]

        # 创建新的DataFrame，以日期为索引
        df2 = df.set_index(date_col)

        # 确保索引是datetime类型
        if not isinstance(df2.index, pd.DatetimeIndex):
            df2.index = pd.to_datetime(df2.index)

        # 提取时间序列数据（数值列）
        ts_data = df2[value_col].values.astype(float)

        # 检测强趋势并给出建议
        if len(ts_data) >= 3:
            # 计算一阶差分的方差
            first_diff = np.diff(ts_data)
            trend_strength = np.std(first_diff) / np.mean(np.abs(first_diff)) if np.mean(np.abs(first_diff)) > 0 else 0

            # 如果趋势很强且d值较大，给出警告
            if trend_strength < 0.3 and d > 0.2:  # 低方差表示强趋势
                print(f"警告：检测到强线性趋势，建议使用较小的d值（如0.1）或考虑使用ARIMA算法")

        # 对于强趋势数据，检测并建议使用ARIMA
        use_detrending = False
        trend_coef = 0
        intercept = 0

        # 检查是否为强线性趋势
        if len(ts_data) >= 3:
            x = np.arange(len(ts_data))
            trend_coef = np.polyfit(x, ts_data, 1)[0]
            intercept = np.polyfit(x, ts_data, 1)[1]

            # 计算线性拟合的R²
            trend_line = trend_coef * x + intercept
            ss_res = np.sum((ts_data - trend_line) ** 2)
            ss_tot = np.sum((ts_data - np.mean(ts_data)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

            # 检查是否应该使用线性预测方法
            use_linear_prediction = False

            # 条件1：强线性趋势 (R²>0.95)
            if r_squared > 0.95:
                use_linear_prediction = True
                print(f"检测到强线性趋势 (R²={r_squared:.3f})，自动切换到线性预测方法")

            # 条件2：明显的增长趋势且R²合理 (R²>0.7 且最近趋势明显)
            elif r_squared > 0.7 and len(ts_data) >= 5:
                recent_diffs = np.diff(ts_data[-5:])  # 最近4个差分
                recent_trend = np.mean(recent_diffs)
                overall_trend = trend_coef

                # 如果最近趋势和整体趋势都是正向且显著
                if recent_trend > 0 and overall_trend > 0 and abs(recent_trend) > np.std(ts_data) * 0.1:
                    use_linear_prediction = True
                    print(f"检测到明显增长趋势 (R²={r_squared:.3f}, 最近趋势={recent_trend:.1f})，使用线性预测方法")

            if use_linear_prediction:
                # 使用线性外推
                future_dates = detect_frequency_and_generate_dates(df2.index, n)

                # 计算线性预测
                future_x = np.arange(len(ts_data), len(ts_data) + n)
                linear_predictions = trend_coef * future_x + intercept

                # 创建结果DataFrame
                if include_confidence_intervals:
                    # 对于线性趋势，置信区间可以基于残差的标准差
                    residuals = ts_data - trend_line
                    residual_std = np.std(residuals)

                    # 计算置信区间（基于t分布）
                    from scipy import stats
                    t_value = stats.t.ppf(1 - alpha/2, len(ts_data) - 2)
                    margin_error = t_value * residual_std

                    result_df = pd.DataFrame({
                        'index': future_dates.strftime('%Y-%m-%d'),
                        'predicted_mean': linear_predictions,
                        'predicted_lower': linear_predictions - margin_error,
                        'predicted_upper': linear_predictions + margin_error
                    })
                else:
                    result_df = pd.DataFrame({
                        'index': future_dates.strftime('%Y-%m-%d'),
                        'predicted_mean': linear_predictions
                    })

                return result_df

            # 对于非强线性趋势，继续使用ARFIMA
            ts_data_for_arfima = ts_data
        else:
            ts_data_for_arfima = ts_data

        # 步骤1：应用分数差分
        frac_diff_data = _fractional_diff(ts_data_for_arfima, d)

        # 步骤2：对分数差分后的数据拟合ARIMA模型
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")

            # 使用SARIMAX拟合ARIMA模型
            arima_model = SARIMAX(frac_diff_data, order=(ar_order, 0, ma_order))
            arima_fit = arima_model.fit(disp=False)

        if include_confidence_intervals:
            # 获取带置信区间的预测
            forecast_result = arima_fit.get_forecast(steps=n)
            forecast_summary = forecast_result.summary_frame(alpha=alpha)

            # 步骤3：对预测结果应用逆分数差分
            mean_values = forecast_summary['mean'].values if hasattr(forecast_summary['mean'], 'values') else forecast_summary['mean']
            lower_values = forecast_summary['mean_ci_lower'].values if hasattr(forecast_summary['mean_ci_lower'], 'values') else forecast_summary['mean_ci_lower']
            upper_values = forecast_summary['mean_ci_upper'].values if hasattr(forecast_summary['mean_ci_upper'], 'values') else forecast_summary['mean_ci_upper']

            predicted_mean = _inverse_fractional_diff(mean_values, d, ts_data_for_arfima, n)
            predicted_lower = _inverse_fractional_diff(lower_values, d, ts_data_for_arfima, n)
            predicted_upper = _inverse_fractional_diff(upper_values, d, ts_data_for_arfima, n)

            # 如果使用了去趋势，需要加回趋势
            if use_detrending:
                future_x = np.arange(len(ts_data), len(ts_data) + n)
                future_trend = trend_coef * future_x + intercept
                predicted_mean += future_trend
                predicted_lower += future_trend
                predicted_upper += future_trend

            # 生成未来日期
            future_dates = detect_frequency_and_generate_dates(df2.index, n)

            # 创建结果DataFrame
            result_df = pd.DataFrame({
                'index': future_dates.strftime('%Y-%m-%d'),
                'predicted_mean': predicted_mean,
                'predicted_lower': predicted_lower,
                'predicted_upper': predicted_upper
            })

        else:
            # 获取点预测
            predictions = arima_fit.forecast(steps=n)

            # 步骤3：对预测结果应用逆分数差分
            pred_values = predictions.values if hasattr(predictions, 'values') else predictions
            predicted_values = _inverse_fractional_diff(pred_values, d, ts_data_for_arfima, n)

            # 如果使用了去趋势，需要加回趋势
            if use_detrending:
                future_x = np.arange(len(ts_data), len(ts_data) + n)
                future_trend = trend_coef * future_x + intercept
                predicted_values += future_trend

            # 生成未来日期
            future_dates = detect_frequency_and_generate_dates(df2.index, n)

            # 创建结果DataFrame
            result_df = pd.DataFrame({
                'index': future_dates.strftime('%Y-%m-%d'),
                'predicted_mean': predicted_values
            })

        return result_df

    except Exception as e:
        print(f"ARFIMA预测过程中发生错误: {str(e)}")
        return None


def LightGBM_predict(df, n, sequence_length=10, num_leaves=31, learning_rate=0.1,
                     feature_fraction=0.9, bagging_fraction=0.8, bagging_freq=5,
                     verbose=-1, random_state=42, num_boost_round=100,
                     early_stopping_rounds=10, include_confidence_intervals=False):
    """
    使用LightGBM算法进行预测
    适用于具有复杂非线性关系的时间序列
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param sequence_length: 输入序列长度，用于训练的时间窗口大小
    :param num_leaves: 树的叶子节点数，控制模型复杂度
    :param learning_rate: 学习率
    :param feature_fraction: 特征采样比例
    :param bagging_fraction: 数据采样比例
    :param bagging_freq: 数据采样频率
    :param verbose: 训练过程输出控制，-1表示静默
    :param random_state: 随机种子，用于结果的可重现性
    :param num_boost_round: 提升轮数
    :param early_stopping_rounds: 早停轮数
    :param include_confidence_intervals: 是否包含置信区间，默认False（基于多次预测的标准差估计）
    :return: 预测结果DataFrame，包含'index'和'predicted_mean'列；如果include_confidence_intervals=True，还包含'predicted_lower'和'predicted_upper'列
    """
    # 检查LightGBM是否可用
    if not LIGHTGBM_AVAILABLE:
        raise ImportError("LightGBM未安装。请运行: pip install lightgbm>=3.0.0")

    # 检查输入df是否正好有2列
    if len(df.columns) != 2:
        print(f"错误：输入数据必须包含2列（日期列和数值列），但实际包含 {len(df.columns)} 列")
        return None

    # 检查数据长度是否足够
    if len(df) < sequence_length + 1:
        raise ValueError(f"数据长度不足，至少需要 {sequence_length + 1} 个观测值进行LightGBM训练")

    try:
        # 准备数据
        df_copy = df.copy()

        # 确保日期列是索引
        if not isinstance(df_copy.index, pd.DatetimeIndex):
            df_copy = df_copy.set_index(df_copy.columns[0])

        # 提取数值序列（第二列，即数值列）
        values = df_copy.iloc[:, 0].values.astype(np.float64)

        # 检测是否为强线性趋势，如果是则使用简化的线性预测
        if len(values) >= 5:
            x_vals = np.arange(len(values))
            slope, intercept = np.polyfit(x_vals, values, 1)

            # 计算线性拟合的R²
            linear_pred = slope * x_vals + intercept
            ss_res = np.sum((values - linear_pred) ** 2)
            ss_tot = np.sum((values - np.mean(values)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

            # 如果R²很高，说明是强线性趋势，直接使用线性外推
            if r_squared > 0.98:  # 非常强的线性关系
                print(f"检测到强线性趋势 (R²={r_squared:.4f})，使用线性外推预测")

                # 生成未来日期
                future_dates = detect_frequency_and_generate_dates(df_copy.index, n)

                # 线性外推预测
                future_x = np.arange(len(values), len(values) + n)
                linear_predictions = slope * future_x + intercept

                # 创建结果DataFrame
                if include_confidence_intervals:
                    # 基于残差计算置信区间
                    residuals = values - linear_pred
                    residual_std = np.std(residuals)
                    margin_error = 1.96 * residual_std  # 95%置信区间

                    result_df = pd.DataFrame({
                        'index': future_dates.strftime('%Y-%m-%d'),
                        'predicted_mean': linear_predictions,
                        'predicted_lower': linear_predictions - margin_error,
                        'predicted_upper': linear_predictions + margin_error
                    })
                else:
                    result_df = pd.DataFrame({
                        'index': future_dates.strftime('%Y-%m-%d'),
                        'predicted_mean': linear_predictions
                    })

                return result_df

        # 创建特征和标签（滑动窗口）
        X, y = [], []
        for i in range(sequence_length, len(values)):
            # 基础滑动窗口特征
            window_features = values[i-sequence_length:i].tolist()

            # 添加统计特征
            window_data = values[i-sequence_length:i]
            statistical_features = [
                np.mean(window_data),      # 均值
                np.std(window_data),       # 标准差
                np.min(window_data),       # 最小值
                np.max(window_data),       # 最大值
                window_data[-1] - window_data[0],  # 首尾差值
                np.mean(np.diff(window_data)) if len(window_data) > 1 else 0,  # 平均变化率
            ]

            # 添加更强的趋势特征
            if len(window_data) >= 3:
                # 线性趋势斜率
                x_trend = np.arange(len(window_data))
                trend_slope = np.polyfit(x_trend, window_data, 1)[0]
                statistical_features.append(trend_slope)

                # 添加更多趋势相关特征
                # 最近几个值的平均变化率
                recent_diffs = np.diff(window_data[-3:]) if len(window_data) >= 3 else [0]
                statistical_features.append(np.mean(recent_diffs))

                # 加速度（二阶差分）
                if len(window_data) >= 4:
                    second_diffs = np.diff(window_data, n=2)
                    statistical_features.append(np.mean(second_diffs))
                else:
                    statistical_features.append(0)

                # 位置特征（当前在序列中的位置）
                statistical_features.append(i / len(values))  # 归一化位置

                # 强化线性趋势特征：基于整个历史的线性外推
                if i >= 5:  # 至少需要5个历史点
                    # 使用更多历史数据计算趋势
                    history_length = min(i, 15)  # 最多使用15个历史点
                    history_x = np.arange(history_length)
                    history_y = values[i-history_length:i]
                    global_trend_slope = np.polyfit(history_x, history_y, 1)[0]
                    statistical_features.append(global_trend_slope)

                    # 基于全局趋势的预期下一个值
                    expected_next = values[i-1] + global_trend_slope
                    statistical_features.append(expected_next)
                else:
                    statistical_features.extend([0, 0])

            else:
                statistical_features.extend([0, 0, 0, i / len(values), 0, 0])

            # 合并所有特征
            all_features = window_features + statistical_features
            X.append(all_features)
            y.append(values[i])

        X, y = np.array(X), np.array(y)

        # 分割训练集和验证集
        if len(X) >= 20:  # 如果数据足够，使用验证集
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]

            # 创建LightGBM数据集
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)

            # 设置参数
            params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': num_leaves,
                'learning_rate': learning_rate,
                'feature_fraction': feature_fraction,
                'bagging_fraction': bagging_fraction,
                'bagging_freq': bagging_freq,
                'verbose': verbose,
                'random_state': random_state,
                'min_data_in_leaf': 1,  # 允许更小的叶子节点
                'min_gain_to_split': 0.0,  # 降低分裂阈值
                'lambda_l2': 0.0,  # 减少正则化以便更好地拟合趋势
            }

            # 训练模型
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                model = lgb.train(
                    params,
                    train_data,
                    valid_sets=[val_data],
                    num_boost_round=num_boost_round,
                    callbacks=[lgb.early_stopping(early_stopping_rounds), lgb.log_evaluation(0)]
                )
        else:
            # 数据不够，不使用验证集
            train_data = lgb.Dataset(X, label=y)

            params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': num_leaves,
                'learning_rate': learning_rate,
                'feature_fraction': feature_fraction,
                'bagging_fraction': bagging_fraction,
                'bagging_freq': bagging_freq,
                'verbose': verbose,
                'random_state': random_state,
                'min_data_in_leaf': 1,  # 允许更小的叶子节点
                'min_gain_to_split': 0.0,  # 降低分裂阈值
                'lambda_l2': 0.0,  # 减少正则化以便更好地拟合趋势
            }

            # 训练模型
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                model = lgb.train(
                    params,
                    train_data,
                    num_boost_round=min(num_boost_round, 50)  # 减少轮数避免过拟合
                )

        # 进行预测
        predictions = []
        current_sequence = values[-sequence_length:].copy()

        # 如果需要置信区间，进行多次预测
        if include_confidence_intervals:
            all_predictions = []
            n_iterations = 20  # 增加迭代次数以获得更好的统计量

            # 计算历史数据的残差标准差作为不确定性的基础
            if len(X) >= 10:
                # 使用模型对训练数据进行预测，计算残差
                train_predictions = model.predict(X[-10:])  # 使用最后10个样本
                residuals = y[-10:] - train_predictions
                residual_std = np.std(residuals)
            else:
                residual_std = np.std(values) * 0.1

            for iteration in range(n_iterations):
                iter_predictions = []
                iter_sequence = current_sequence.copy()

                # 为每次迭代添加不同的噪声
                if iteration > 0:
                    # 添加噪声到初始序列
                    noise_factor = np.random.uniform(0.5, 1.5)  # 随机噪声因子
                    iter_sequence += np.random.normal(0, residual_std * noise_factor, len(iter_sequence))

                for step in range(n):
                    # 准备特征
                    window_features = iter_sequence.tolist()

                    # 统计特征
                    statistical_features = [
                        np.mean(iter_sequence),
                        np.std(iter_sequence),
                        np.min(iter_sequence),
                        np.max(iter_sequence),
                        iter_sequence[-1] - iter_sequence[0],
                        np.mean(np.diff(iter_sequence)) if len(iter_sequence) > 1 else 0,
                    ]

                    # 添加更强的趋势特征（与训练时保持一致）
                    if len(iter_sequence) >= 3:
                        x_trend = np.arange(len(iter_sequence))
                        trend_slope = np.polyfit(x_trend, iter_sequence, 1)[0]
                        statistical_features.append(trend_slope)

                        # 最近几个值的平均变化率
                        recent_diffs = np.diff(iter_sequence[-3:]) if len(iter_sequence) >= 3 else [0]
                        statistical_features.append(np.mean(recent_diffs))

                        # 加速度（二阶差分）
                        if len(iter_sequence) >= 4:
                            second_diffs = np.diff(iter_sequence, n=2)
                            statistical_features.append(np.mean(second_diffs))
                        else:
                            statistical_features.append(0)

                        # 位置特征
                        current_position = (len(values) + step) / (len(values) + n)
                        statistical_features.append(current_position)

                        # 强化线性趋势特征
                        history_length = min(len(values), 15)
                        history_x = np.arange(history_length)
                        history_y = values[-history_length:]
                        global_trend_slope = np.polyfit(history_x, history_y, 1)[0]
                        statistical_features.append(global_trend_slope)

                        # 基于全局趋势的预期下一个值
                        expected_next = iter_sequence[-1] + global_trend_slope
                        statistical_features.append(expected_next)

                    else:
                        current_position = (len(values) + step) / (len(values) + n)
                        # 简化版本的全局趋势
                        if len(values) >= 2:
                            simple_trend = values[-1] - values[-2]
                            expected_next = iter_sequence[-1] + simple_trend
                        else:
                            simple_trend = 0
                            expected_next = iter_sequence[-1]
                        statistical_features.extend([0, 0, 0, current_position, simple_trend, expected_next])

                    # 合并特征
                    all_features = window_features + statistical_features
                    input_features = np.array(all_features).reshape(1, -1)

                    # 预测下一个值
                    next_pred = model.predict(input_features)[0]

                    # 为预测值添加一些随机性（基于残差标准差）
                    if iteration > 0:
                        prediction_noise = np.random.normal(0, residual_std * 0.5)
                        next_pred += prediction_noise

                    iter_predictions.append(next_pred)

                    # 更新序列（滑动窗口）
                    iter_sequence = np.append(iter_sequence[1:], next_pred)

                all_predictions.append(iter_predictions)

            # 计算统计量
            all_predictions = np.array(all_predictions)
            predictions = np.mean(all_predictions, axis=0)
            prediction_std = np.std(all_predictions, axis=0)

            # 计算置信区间（假设正态分布）
            confidence_level = 0.95
            z_score = 1.96  # 95%置信区间
            margin_error = z_score * prediction_std

        else:
            # 单次预测
            for step in range(n):
                # 准备特征
                window_features = current_sequence.tolist()

                # 统计特征
                statistical_features = [
                    np.mean(current_sequence),
                    np.std(current_sequence),
                    np.min(current_sequence),
                    np.max(current_sequence),
                    current_sequence[-1] - current_sequence[0],
                    np.mean(np.diff(current_sequence)) if len(current_sequence) > 1 else 0,
                ]

                # 添加更强的趋势特征（与训练时保持一致）
                if len(current_sequence) >= 3:
                    x_trend = np.arange(len(current_sequence))
                    trend_slope = np.polyfit(x_trend, current_sequence, 1)[0]
                    statistical_features.append(trend_slope)

                    # 最近几个值的平均变化率
                    recent_diffs = np.diff(current_sequence[-3:]) if len(current_sequence) >= 3 else [0]
                    statistical_features.append(np.mean(recent_diffs))

                    # 加速度（二阶差分）
                    if len(current_sequence) >= 4:
                        second_diffs = np.diff(current_sequence, n=2)
                        statistical_features.append(np.mean(second_diffs))
                    else:
                        statistical_features.append(0)

                    # 位置特征（预测步数的归一化位置）
                    current_position = (len(values) + step) / (len(values) + n)
                    statistical_features.append(current_position)

                    # 强化线性趋势特征：基于历史数据的全局趋势
                    # 使用原始数据的最后15个点计算全局趋势
                    history_length = min(len(values), 15)
                    history_x = np.arange(history_length)
                    history_y = values[-history_length:]
                    global_trend_slope = np.polyfit(history_x, history_y, 1)[0]
                    statistical_features.append(global_trend_slope)

                    # 基于全局趋势的预期下一个值
                    expected_next = current_sequence[-1] + global_trend_slope
                    statistical_features.append(expected_next)

                else:
                    current_position = (len(values) + step) / (len(values) + n)
                    # 简化版本的全局趋势
                    if len(values) >= 2:
                        simple_trend = values[-1] - values[-2]
                        expected_next = current_sequence[-1] + simple_trend
                    else:
                        simple_trend = 0
                        expected_next = current_sequence[-1]
                    statistical_features.extend([0, 0, 0, current_position, simple_trend, expected_next])

                # 合并特征
                all_features = window_features + statistical_features
                input_features = np.array(all_features).reshape(1, -1)

                # 预测下一个值
                next_pred = model.predict(input_features)[0]
                predictions.append(next_pred)

                # 更新序列（滑动窗口）
                current_sequence = np.append(current_sequence[1:], next_pred)

        # 生成未来日期
        future_dates = detect_frequency_and_generate_dates(df_copy.index, n)

        # 创建预测结果DataFrame
        if include_confidence_intervals:
            result_df = pd.DataFrame({
                'index': future_dates.strftime('%Y-%m-%d'),
                'predicted_mean': predictions,
                'predicted_lower': predictions - margin_error,
                'predicted_upper': predictions + margin_error
            })
        else:
            result_df = pd.DataFrame({
                'index': future_dates.strftime('%Y-%m-%d'),
                'predicted_mean': predictions
            })

        return result_df

    except Exception as e:
        print(f"LightGBM预测过程中发生错误: {str(e)}")
        return None


def detect_frequency_and_generate_dates(df_index, n):
    """
    检测时间序列的频率并生成相应的未来日期
    :param df_index: DataFrame的日期索引
    :param n: 要生成的未来日期数量
    :return: 未来日期列表
    """
    # 确保索引是datetime类型
    if not isinstance(df_index, pd.DatetimeIndex):
        df_index = pd.to_datetime(df_index)

    if len(df_index) < 2:
        # 如果只有一个日期，默认按天生成
        last_date = df_index[-1]
        future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), periods=n, freq='D')
        return future_dates

    # 计算日期间隔
    intervals = df_index[1:] - df_index[:-1]
    # 找到最常见的间隔
    interval_counts = intervals.value_counts()
    most_common_interval = interval_counts.index[0] if len(interval_counts) > 0 else intervals[0]

    last_date = df_index[-1]

    # 判断频率类型
    if most_common_interval.days == 1:
        # 按天
        future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), periods=n, freq='D')
    elif 28 <= most_common_interval.days <= 31:
        # 按月 - 需要特殊处理月初和月末
        future_dates = []
        current_date = last_date

        # 检查是否是月初（1号）
        if last_date.day == 1:
            # 每月1号
            for i in range(n):
                current_date = current_date + relativedelta(months=1)
                future_dates.append(current_date)
        else:
            # 检查是否是月末 - 判断当前日期是否是该月的最后一天
            next_month_first = (last_date.replace(day=1) + relativedelta(months=1))
            current_month_last = next_month_first - pd.Timedelta(days=1)

            if last_date.day == current_month_last.day:
                # 每月最后一天
                for i in range(n):
                    # 移动到下个月，然后获取该月最后一天
                    next_month_first = (current_date.replace(day=1) + relativedelta(months=1))
                    current_date = next_month_first + relativedelta(months=1) - pd.Timedelta(days=1)
                    future_dates.append(current_date)
            else:
                # 每月固定日期
                for i in range(n):
                    current_date = current_date + relativedelta(months=1)
                    future_dates.append(current_date)

        future_dates = pd.DatetimeIndex(future_dates)
    elif 360 <= most_common_interval.days <= 370:
        # 按年
        future_dates = []
        current_date = last_date
        for i in range(n):
            current_date = current_date + relativedelta(years=1)
            future_dates.append(current_date)
        future_dates = pd.DatetimeIndex(future_dates)
    elif 7 <= most_common_interval.days <= 7:
        # 按周
        future_dates = pd.date_range(start=last_date + pd.Timedelta(weeks=1), periods=n, freq='W')
    elif 90 <= most_common_interval.days <= 92:
        # 按季度
        future_dates = []
        current_date = last_date
        for i in range(n):
            current_date = current_date + relativedelta(months=3)
            future_dates.append(current_date)
        future_dates = pd.DatetimeIndex(future_dates)
    else:
        # 其他情况，使用检测到的间隔
        future_dates = []
        current_date = last_date
        for i in range(n):
            current_date = current_date + most_common_interval
            future_dates.append(current_date)
        future_dates = pd.DatetimeIndex(future_dates)

    return future_dates
