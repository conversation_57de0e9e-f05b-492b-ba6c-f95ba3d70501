import unittest
import pandas as pd
import numpy as np
from datetime import datetime, date
import sys
import os

# Add the parent directory to the path to import woo_utils
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import the specific module directly to avoid dependency issues
import importlib.util
spec = importlib.util.spec_from_file_location("predict", os.path.join(os.path.dirname(__file__), '..', 'woo_utils', 'predict.py'))
predict = importlib.util.module_from_spec(spec)
spec.loader.exec_module(predict)

# Import all prediction functions
AR_predict = predict.AR_predict
ARIMA_predict = predict.ARIMA_predict
ARFIMA_predict = predict.ARFIMA_predict
single_ES_predict = predict.single_ES_predict
double_ES_predict = predict.double_ES_predict
triple_ES_predict = predict.triple_ES_predict
prophet_predict = predict.prophet_predict
RNN_predict = predict.RNN_predict
LightGBM_predict = predict.LightGBM_predict
detect_frequency_and_generate_dates = predict.detect_frequency_and_generate_dates

class TestProphetPredict(unittest.TestCase):
    """Test cases for the prophet_predict function"""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample daily data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        values = 100 + np.sin(np.arange(100) * 2 * np.pi / 7) * 10 + np.random.normal(0, 2, 100)  # Weekly pattern with noise
        self.daily_df = pd.DataFrame({
            'date': dates.strftime('%Y-%m-%d'),
            'value': values
        })
        
        # Create sample monthly data
        monthly_dates = pd.date_range('2020-01-01', periods=36, freq='MS')  # 3 years of monthly data
        monthly_values = 1000 + np.sin(np.arange(36) * 2 * np.pi / 12) * 200 + np.random.normal(0, 50, 36)  # Yearly pattern
        self.monthly_df = pd.DataFrame({
            'date': monthly_dates.strftime('%Y-%m-%d'),
            'value': monthly_values
        })
        
        # Create simple trend data
        trend_dates = pd.date_range('2023-01-01', periods=50, freq='D')
        trend_values = 100 + np.arange(50) * 2 + np.random.normal(0, 5, 50)  # Linear trend
        self.trend_df = pd.DataFrame({
            'date': trend_dates.strftime('%Y-%m-%d'),
            'value': trend_values
        })
        
        # Create minimal data (edge case)
        self.minimal_df = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02'],
            'value': [100, 105]
        })

    def test_prophet_predict_basic_functionality(self):
        """Test basic Prophet prediction functionality"""
        result = prophet_predict(self.daily_df, n=7)
        
        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(list(result.columns), ['index', 'predicted_mean'])
        self.assertEqual(len(result), 7)
        
        # Check that predictions are reasonable (not NaN or infinite)
        self.assertFalse(result['predicted_mean'].isna().any())
        self.assertTrue(np.isfinite(result['predicted_mean']).all())
        
        # Check date format
        self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
        self.assertTrue(all('-' in date_str for date_str in result['index']))

    def test_prophet_predict_monthly_data(self):
        """Test Prophet prediction with monthly data"""
        result = prophet_predict(self.monthly_df, n=12)
        
        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 12)
        
        # Check that predictions are reasonable
        self.assertFalse(result['predicted_mean'].isna().any())
        self.assertTrue(np.isfinite(result['predicted_mean']).all())
        
        # Check that predicted values are in reasonable range
        original_mean = self.monthly_df['value'].mean()
        predicted_mean = result['predicted_mean'].mean()
        # Predictions should be within 50% of original mean (reasonable for test data)
        self.assertLess(abs(predicted_mean - original_mean) / original_mean, 0.5)

    def test_prophet_predict_with_trend(self):
        """Test Prophet prediction with trending data"""
        result = prophet_predict(self.trend_df, n=10)
        
        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 10)
        
        # Check that predictions continue the trend (should be increasing)
        # Since we have a positive trend, predictions should generally increase
        last_original_value = self.trend_df['value'].iloc[-1]
        first_predicted_value = result['predicted_mean'].iloc[0]
        
        # First prediction should be reasonably close to last observed value
        self.assertLess(abs(first_predicted_value - last_original_value), 50)

    def test_prophet_predict_custom_parameters(self):
        """Test Prophet prediction with custom parameters"""
        result = prophet_predict(
            self.daily_df, 
            n=5,
            yearly_seasonality=False,
            weekly_seasonality=True,
            daily_seasonality=False,
            seasonality_mode='multiplicative',
            changepoint_prior_scale=0.1
        )
        
        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 5)
        self.assertFalse(result['predicted_mean'].isna().any())

    def test_prophet_predict_minimal_data(self):
        """Test Prophet prediction with minimal data"""
        result = prophet_predict(self.minimal_df, n=3)
        
        # Should still work with minimal data
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertFalse(result['predicted_mean'].isna().any())

    def test_prophet_predict_invalid_input(self):
        """Test Prophet prediction with invalid input"""
        # Test with wrong number of columns
        invalid_df = pd.DataFrame({
            'date': ['2023-01-01'],
            'value1': [100],
            'value2': [200]
        })
        
        result = prophet_predict(invalid_df, n=5)
        self.assertIsNone(result)

    def test_prophet_predict_single_prediction(self):
        """Test Prophet prediction for single future point"""
        result = prophet_predict(self.daily_df, n=1)
        
        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 1)
        self.assertFalse(result['predicted_mean'].isna().any())

    def test_prophet_predict_large_horizon(self):
        """Test Prophet prediction with larger prediction horizon"""
        result = prophet_predict(self.monthly_df, n=24)  # 2 years ahead
        
        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 24)
        self.assertFalse(result['predicted_mean'].isna().any())

    def test_prophet_predict_date_continuity(self):
        """Test that predicted dates are continuous from input data"""
        result = prophet_predict(self.daily_df, n=5)
        
        # Get last date from input data
        last_input_date = pd.to_datetime(self.daily_df['date'].iloc[-1])
        first_predicted_date = pd.to_datetime(result['index'].iloc[0])
        
        # First predicted date should be one day after last input date
        expected_date = last_input_date + pd.Timedelta(days=1)
        self.assertEqual(first_predicted_date, expected_date)

    def test_original_dataframe_not_modified(self):
        """Test that original DataFrame is not modified"""
        original_df = self.daily_df.copy()
        
        prophet_predict(self.daily_df, n=5)
        
        # Check that original DataFrame is unchanged
        pd.testing.assert_frame_equal(self.daily_df, original_df)

    def test_prophet_predict_changepoint_parameters(self):
        """Test Prophet prediction with custom changepoint parameters"""
        # Test with custom n_changepoints
        result1 = prophet_predict(
            self.daily_df,
            n=5,
            n_changepoints=10,
            changepoint_range=0.9
        )

        # Check structure
        self.assertIsNotNone(result1)
        self.assertEqual(len(result1), 5)
        self.assertFalse(result1['predicted_mean'].isna().any())

        # Test with different changepoint parameters
        result2 = prophet_predict(
            self.daily_df,
            n=5,
            n_changepoints=50,
            changepoint_range=0.6
        )

        # Check structure
        self.assertIsNotNone(result2)
        self.assertEqual(len(result2), 5)
        self.assertFalse(result2['predicted_mean'].isna().any())

        # Results should be different due to different changepoint settings
        # (though they might be similar for simple test data)
        self.assertEqual(len(result1), len(result2))


class TestARIMAPredict(unittest.TestCase):
    """Test cases for the ARIMA_predict function"""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample daily data with trend
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        trend = np.arange(100) * 0.5
        noise = np.random.normal(0, 2, 100)
        values = 100 + trend + noise
        self.daily_df = pd.DataFrame({
            'date': dates.strftime('%Y-%m-%d'),
            'value': values
        })

        # Create sample monthly data
        monthly_dates = pd.date_range('2020-01-01', periods=36, freq='MS')
        monthly_trend = np.arange(36) * 2
        monthly_noise = np.random.normal(0, 10, 36)
        monthly_values = 1000 + monthly_trend + monthly_noise
        self.monthly_df = pd.DataFrame({
            'date': monthly_dates.strftime('%Y-%m-%d'),
            'value': monthly_values
        })

    def test_arima_predict_basic_functionality(self):
        """Test basic ARIMA prediction functionality without confidence intervals"""
        result = ARIMA_predict(self.daily_df, n=7, order=(1, 1, 1))

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(list(result.columns), ['index', 'predicted_mean'])
        self.assertEqual(len(result), 7)

        # Check that predictions are reasonable (not NaN or infinite)
        self.assertFalse(result['predicted_mean'].isna().any())
        self.assertTrue(np.isfinite(result['predicted_mean']).all())

        # Check date format
        self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
        self.assertTrue(all('-' in date_str for date_str in result['index']))

    def test_arima_predict_with_confidence_intervals(self):
        """Test ARIMA prediction with confidence intervals"""
        result = ARIMA_predict(self.daily_df, n=5, order=(1, 1, 1),
                              include_confidence_intervals=True)

        # Check structure
        self.assertIsNotNone(result)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)
        self.assertEqual(len(result), 5)

        # Check that predictions are reasonable (not NaN or infinite)
        for col in ['predicted_mean', 'predicted_lower', 'predicted_upper']:
            self.assertFalse(result[col].isna().any())
            self.assertTrue(np.isfinite(result[col]).all())

        # Check that confidence intervals make sense (lower < mean < upper)
        self.assertTrue((result['predicted_lower'] <= result['predicted_mean']).all())
        self.assertTrue((result['predicted_mean'] <= result['predicted_upper']).all())

        # Check date format
        self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
        self.assertTrue(all('-' in date_str for date_str in result['index']))

    def test_arima_predict_custom_alpha(self):
        """Test ARIMA prediction with custom confidence level"""
        # Test with 90% confidence interval (alpha=0.1)
        result = ARIMA_predict(self.daily_df, n=3, order=(1, 1, 1),
                              include_confidence_intervals=True, alpha=0.1)

        # Check structure
        self.assertIsNotNone(result)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)
        self.assertEqual(len(result), 3)

        # Check that confidence intervals make sense
        self.assertTrue((result['predicted_lower'] <= result['predicted_mean']).all())
        self.assertTrue((result['predicted_mean'] <= result['predicted_upper']).all())

    def test_arima_predict_seasonal(self):
        """Test ARIMA prediction with seasonal order"""
        result = ARIMA_predict(self.monthly_df, n=12, order=(1, 1, 1),
                              seasonal_order=(1, 1, 1, 12),
                              include_confidence_intervals=True)

        # Check structure
        self.assertIsNotNone(result)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)
        self.assertEqual(len(result), 12)

        # Check that predictions are reasonable
        for col in ['predicted_mean', 'predicted_lower', 'predicted_upper']:
            self.assertFalse(result[col].isna().any())
            self.assertTrue(np.isfinite(result[col]).all())

    def test_arima_predict_invalid_input(self):
        """Test ARIMA prediction with invalid input"""
        # Test with wrong number of columns
        invalid_df = pd.DataFrame({
            'date': ['2023-01-01'],
            'value1': [100],
            'value2': [200]
        })

        result = ARIMA_predict(invalid_df, n=5, order=(1, 1, 1))
        self.assertIsNone(result)

    def test_arima_predict_single_prediction(self):
        """Test ARIMA prediction for single future point with confidence intervals"""
        result = ARIMA_predict(self.daily_df, n=1, order=(1, 1, 1),
                              include_confidence_intervals=True)

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 1)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)

        # Check that predictions are reasonable
        for col in ['predicted_mean', 'predicted_lower', 'predicted_upper']:
            self.assertFalse(result[col].isna().any())
            self.assertTrue(np.isfinite(result[col]).all())


class TestRNNPredict(unittest.TestCase):
    """Test cases for the RNN_predict function"""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample daily data with trend and seasonality
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        # Create a more complex pattern for RNN to learn
        trend = np.arange(50) * 0.5
        seasonal = 10 * np.sin(np.arange(50) * 2 * np.pi / 7)  # Weekly pattern
        noise = np.random.normal(0, 2, 50)
        values = 100 + trend + seasonal + noise

        self.daily_df = pd.DataFrame({
            'date': dates.strftime('%Y-%m-%d'),
            'value': values
        })

        # Create longer dataset for better RNN training
        long_dates = pd.date_range('2022-01-01', periods=100, freq='D')
        long_trend = np.arange(100) * 0.3
        long_seasonal = 15 * np.sin(np.arange(100) * 2 * np.pi / 7)
        long_noise = np.random.normal(0, 3, 100)
        long_values = 200 + long_trend + long_seasonal + long_noise

        self.long_df = pd.DataFrame({
            'date': long_dates.strftime('%Y-%m-%d'),
            'value': long_values
        })

        # Create minimal data (edge case)
        self.minimal_df = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02'],
            'value': [100, 105]
        })

    def test_rnn_predict_basic_functionality(self):
        """Test basic RNN prediction functionality"""
        # Use smaller parameters for faster testing
        result = RNN_predict(self.long_df, n=5, sequence_length=10,
                           hidden_layer_sizes=(20, 10), max_iter=50)

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(list(result.columns), ['index', 'predicted_mean'])
        self.assertEqual(len(result), 5)

        # Check that predictions are reasonable (not NaN or infinite)
        self.assertFalse(result['predicted_mean'].isna().any())
        self.assertTrue(np.isfinite(result['predicted_mean']).all())

        # Check date format
        self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
        self.assertTrue(all('-' in date_str for date_str in result['index']))

    def test_rnn_predict_custom_parameters(self):
        """Test RNN prediction with custom parameters"""
        result = RNN_predict(
            self.long_df,
            n=3,
            sequence_length=15,
            hidden_layer_sizes=(30, 15),
            max_iter=30,
            learning_rate_init=0.01,
            alpha=0.001,
            random_state=123
        )

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertFalse(result['predicted_mean'].isna().any())

    def test_rnn_predict_invalid_input(self):
        """Test RNN prediction with invalid input"""
        # Test with wrong number of columns
        invalid_df = pd.DataFrame({
            'date': ['2023-01-01'],
            'value1': [100],
            'value2': [200]
        })

        result = RNN_predict(invalid_df, n=5, max_iter=10)
        self.assertIsNone(result)

    def test_rnn_predict_insufficient_data(self):
        """Test RNN prediction with insufficient data"""
        # Test with data length less than sequence_length + 1
        with self.assertRaises(ValueError):
            RNN_predict(self.minimal_df, n=5, sequence_length=10, max_iter=10)

    def test_rnn_predict_single_prediction(self):
        """Test RNN prediction for single future point"""
        result = RNN_predict(self.long_df, n=1, sequence_length=10,
                           hidden_layer_sizes=(20, 10), max_iter=30)

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 1)
        self.assertFalse(result['predicted_mean'].isna().any())

    def test_rnn_predict_date_continuity(self):
        """Test that predicted dates are continuous from input data"""
        result = RNN_predict(self.long_df, n=3, sequence_length=10,
                           hidden_layer_sizes=(20, 10), max_iter=30)

        # Get last date from input data
        last_input_date = pd.to_datetime(self.long_df['date'].iloc[-1])
        first_predicted_date = pd.to_datetime(result['index'].iloc[0])

        # First predicted date should be one day after last input date
        expected_date = last_input_date + pd.Timedelta(days=1)
        self.assertEqual(first_predicted_date, expected_date)

    def test_rnn_predict_reasonable_values(self):
        """Test that RNN predictions are in reasonable range"""
        result = RNN_predict(self.long_df, n=5, sequence_length=10,
                           hidden_layer_sizes=(20, 10), max_iter=50)

        # Check that predicted values are in reasonable range
        original_min = self.long_df['value'].min()
        original_max = self.long_df['value'].max()
        predicted_values = result['predicted_mean']

        # Predictions should be within a reasonable range (allow some extrapolation)
        # Allow predictions to be within 2x the original range
        range_extension = (original_max - original_min) * 2
        lower_bound = original_min - range_extension
        upper_bound = original_max + range_extension

        self.assertTrue(all(predicted_values >= lower_bound))
        self.assertTrue(all(predicted_values <= upper_bound))

    def test_original_dataframe_not_modified(self):
        """Test that original DataFrame is not modified"""
        original_df = self.long_df.copy()

        RNN_predict(self.long_df, n=3, sequence_length=10,
                   hidden_layer_sizes=(20, 10), max_iter=30)

        # Check that original DataFrame is unchanged
        pd.testing.assert_frame_equal(self.long_df, original_df)


class TestDetectFrequencyAndGenerateDates(unittest.TestCase):
    """Test cases for the detect_frequency_and_generate_dates function"""

    def test_daily_frequency(self):
        """Test daily frequency detection and date generation"""
        dates = pd.date_range('2023-01-01', periods=10, freq='D')
        result = detect_frequency_and_generate_dates(dates, 5)
        
        self.assertEqual(len(result), 5)
        self.assertEqual(result[0], pd.Timestamp('2023-01-11'))
        self.assertEqual(result[-1], pd.Timestamp('2023-01-15'))

    def test_monthly_frequency(self):
        """Test monthly frequency detection and date generation"""
        dates = pd.date_range('2023-01-01', periods=6, freq='MS')
        result = detect_frequency_and_generate_dates(dates, 3)
        
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0], pd.Timestamp('2023-07-01'))

    def test_weekly_frequency(self):
        """Test weekly frequency detection and date generation"""
        dates = pd.date_range('2023-01-01', periods=4, freq='W')
        result = detect_frequency_and_generate_dates(dates, 2)
        
        self.assertEqual(len(result), 2)

    def test_single_date(self):
        """Test with single date (edge case)"""
        dates = pd.DatetimeIndex(['2023-01-01'])
        result = detect_frequency_and_generate_dates(dates, 3)
        
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0], pd.Timestamp('2023-01-02'))


class TestARFIMAPredict(unittest.TestCase):
    """Test cases for the ARFIMA_predict function"""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample daily data with long memory characteristics
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        # Generate data with long memory pattern
        np.random.seed(42)  # For reproducible tests
        trend = np.arange(100) * 0.3
        long_memory = np.cumsum(np.random.normal(0, 1, 100)) * 0.1  # Random walk component
        noise = np.random.normal(0, 2, 100)
        values = 100 + trend + long_memory + noise

        self.daily_df = pd.DataFrame({
            'date': dates.strftime('%Y-%m-%d'),
            'value': values
        })

        # Create sample monthly data
        monthly_dates = pd.date_range('2020-01-01', periods=60, freq='MS')
        monthly_trend = np.arange(60) * 1.5
        monthly_long_memory = np.cumsum(np.random.normal(0, 1, 60)) * 0.2
        monthly_noise = np.random.normal(0, 10, 60)
        monthly_values = 1000 + monthly_trend + monthly_long_memory + monthly_noise

        self.monthly_df = pd.DataFrame({
            'date': monthly_dates.strftime('%Y-%m-%d'),
            'value': monthly_values
        })

        # Create minimal data (edge case)
        self.minimal_df = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02', '2023-01-03'],
            'value': [100, 105, 102]
        })

    def test_arfima_predict_basic_functionality(self):
        """Test basic ARFIMA prediction functionality without confidence intervals"""
        result = ARFIMA_predict(self.daily_df, n=7, ar_order=1, d=0.3, ma_order=1)

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(list(result.columns), ['index', 'predicted_mean'])
        self.assertEqual(len(result), 7)

        # Check that predictions are reasonable (not NaN or infinite)
        self.assertFalse(result['predicted_mean'].isna().any())
        self.assertTrue(np.isfinite(result['predicted_mean']).all())

        # Check date format
        self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
        self.assertTrue(all('-' in date_str for date_str in result['index']))

    def test_arfima_predict_with_confidence_intervals(self):
        """Test ARFIMA prediction with confidence intervals"""
        result = ARFIMA_predict(self.daily_df, n=5, ar_order=1, d=0.2, ma_order=1,
                               include_confidence_intervals=True)

        # Check structure
        self.assertIsNotNone(result)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)
        self.assertEqual(len(result), 5)

        # Check that predictions are reasonable (not NaN or infinite)
        for col in ['predicted_mean', 'predicted_lower', 'predicted_upper']:
            self.assertFalse(result[col].isna().any())
            self.assertTrue(np.isfinite(result[col]).all())

        # Check that confidence intervals make sense (lower < mean < upper)
        self.assertTrue((result['predicted_lower'] <= result['predicted_mean']).all())
        self.assertTrue((result['predicted_mean'] <= result['predicted_upper']).all())

        # Check date format
        self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
        self.assertTrue(all('-' in date_str for date_str in result['index']))

    def test_arfima_predict_custom_alpha(self):
        """Test ARFIMA prediction with custom confidence level"""
        # Test with 90% confidence interval (alpha=0.1)
        result = ARFIMA_predict(self.daily_df, n=3, ar_order=1, d=0.25, ma_order=1,
                               include_confidence_intervals=True, alpha=0.1)

        # Check structure
        self.assertIsNotNone(result)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)
        self.assertEqual(len(result), 3)

        # Check that confidence intervals make sense
        self.assertTrue((result['predicted_lower'] <= result['predicted_mean']).all())
        self.assertTrue((result['predicted_mean'] <= result['predicted_upper']).all())

    def test_arfima_predict_different_orders(self):
        """Test ARFIMA prediction with different AR and MA orders"""
        # Test ARFIMA(2, 0.3, 2)
        result = ARFIMA_predict(self.daily_df, n=5, ar_order=2, d=0.3, ma_order=2)

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 5)
        self.assertFalse(result['predicted_mean'].isna().any())

        # Test ARFIMA(0, 0.4, 1) - pure fractional MA
        result2 = ARFIMA_predict(self.daily_df, n=3, ar_order=0, d=0.4, ma_order=1)

        # Check structure
        self.assertIsNotNone(result2)
        self.assertEqual(len(result2), 3)
        self.assertFalse(result2['predicted_mean'].isna().any())

    def test_arfima_predict_monthly_data(self):
        """Test ARFIMA prediction with monthly data"""
        result = ARFIMA_predict(self.monthly_df, n=12, ar_order=1, d=0.35, ma_order=1,
                               include_confidence_intervals=True)

        # Check structure
        self.assertIsNotNone(result)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)
        self.assertEqual(len(result), 12)

        # Check that predictions are reasonable
        for col in ['predicted_mean', 'predicted_lower', 'predicted_upper']:
            self.assertFalse(result[col].isna().any())
            self.assertTrue(np.isfinite(result[col]).all())

    def test_arfima_predict_invalid_input(self):
        """Test ARFIMA prediction with invalid input"""
        # Test with wrong number of columns
        invalid_df = pd.DataFrame({
            'date': ['2023-01-01'],
            'value1': [100],
            'value2': [200]
        })

        result = ARFIMA_predict(invalid_df, n=5, ar_order=1, d=0.3, ma_order=1)
        self.assertIsNone(result)

    def test_arfima_predict_insufficient_data(self):
        """Test ARFIMA prediction with insufficient data"""
        # Test with very small dataset
        result = ARFIMA_predict(self.minimal_df, n=5, ar_order=1, d=0.3, ma_order=1)
        self.assertIsNone(result)

    def test_arfima_predict_single_prediction(self):
        """Test ARFIMA prediction for single future point with confidence intervals"""
        result = ARFIMA_predict(self.daily_df, n=1, ar_order=1, d=0.3, ma_order=1,
                               include_confidence_intervals=True)

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 1)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)

        # Check that predictions are reasonable
        for col in ['predicted_mean', 'predicted_lower', 'predicted_upper']:
            self.assertFalse(result[col].isna().any())
            self.assertTrue(np.isfinite(result[col]).all())

    def test_arfima_predict_edge_d_values(self):
        """Test ARFIMA prediction with edge values of d parameter"""
        # Test with d close to boundary values
        result1 = ARFIMA_predict(self.daily_df, n=3, ar_order=1, d=0.49, ma_order=1)
        self.assertIsNotNone(result1)
        self.assertEqual(len(result1), 3)

        result2 = ARFIMA_predict(self.daily_df, n=3, ar_order=1, d=-0.49, ma_order=1)
        self.assertIsNotNone(result2)
        self.assertEqual(len(result2), 3)

        # Test with d=0 (should behave like ARIMA)
        result3 = ARFIMA_predict(self.daily_df, n=3, ar_order=1, d=0.0, ma_order=1)
        self.assertIsNotNone(result3)
        self.assertEqual(len(result3), 3)

    def test_arfima_predict_date_continuity(self):
        """Test that predicted dates are continuous from input data"""
        result = ARFIMA_predict(self.daily_df, n=5, ar_order=1, d=0.3, ma_order=1)

        # Get last date from input data
        last_input_date = pd.to_datetime(self.daily_df['date'].iloc[-1])
        first_predicted_date = pd.to_datetime(result['index'].iloc[0])

        # First predicted date should be one day after last input date
        expected_date = last_input_date + pd.Timedelta(days=1)
        self.assertEqual(first_predicted_date, expected_date)

    def test_original_dataframe_not_modified(self):
        """Test that original DataFrame is not modified"""
        original_df = self.daily_df.copy()

        ARFIMA_predict(self.daily_df, n=5, ar_order=1, d=0.3, ma_order=1)

        # Check that original DataFrame is unchanged
        pd.testing.assert_frame_equal(self.daily_df, original_df)


class TestLightGBMPredict(unittest.TestCase):
    """Test cases for the LightGBM_predict function"""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample daily data with trend and seasonality
        dates = pd.date_range('2023-01-01', periods=60, freq='D')
        # Create a complex pattern for LightGBM to learn
        trend = np.arange(60) * 0.5
        seasonal = 15 * np.sin(np.arange(60) * 2 * np.pi / 7)  # Weekly pattern
        noise = np.random.normal(0, 3, 60)
        values = 100 + trend + seasonal + noise

        self.daily_df = pd.DataFrame({
            'date': dates.strftime('%Y-%m-%d'),
            'value': values
        })

        # Create longer dataset for better LightGBM training
        long_dates = pd.date_range('2022-01-01', periods=120, freq='D')
        long_trend = np.arange(120) * 0.3
        long_seasonal = 20 * np.sin(np.arange(120) * 2 * np.pi / 7)
        long_noise = np.random.normal(0, 4, 120)
        long_values = 200 + long_trend + long_seasonal + long_noise

        self.long_df = pd.DataFrame({
            'date': long_dates.strftime('%Y-%m-%d'),
            'value': long_values
        })

        # Create minimal data (edge case)
        self.minimal_df = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02'],
            'value': [100, 105]
        })

    def test_lightgbm_predict_basic_functionality(self):
        """Test basic LightGBM prediction functionality"""
        # Use smaller parameters for faster testing
        result = LightGBM_predict(self.long_df, n=5, sequence_length=10,
                                 num_boost_round=20, num_leaves=15)

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(list(result.columns), ['index', 'predicted_mean'])
        self.assertEqual(len(result), 5)

        # Check that predictions are reasonable (not NaN or infinite)
        self.assertFalse(result['predicted_mean'].isna().any())
        self.assertTrue(np.isfinite(result['predicted_mean']).all())

        # Check date format
        self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
        self.assertTrue(all('-' in date_str for date_str in result['index']))

    def test_lightgbm_predict_with_confidence_intervals(self):
        """Test LightGBM prediction with confidence intervals"""
        result = LightGBM_predict(self.long_df, n=3, sequence_length=10,
                                 num_boost_round=15, num_leaves=10,
                                 include_confidence_intervals=True)

        # Check structure
        self.assertIsNotNone(result)
        expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
        self.assertEqual(list(result.columns), expected_columns)
        self.assertEqual(len(result), 3)

        # Check that predictions are reasonable (not NaN or infinite)
        for col in ['predicted_mean', 'predicted_lower', 'predicted_upper']:
            self.assertFalse(result[col].isna().any())
            self.assertTrue(np.isfinite(result[col]).all())

        # Check that confidence intervals make sense (lower <= mean <= upper)
        self.assertTrue((result['predicted_lower'] <= result['predicted_mean']).all())
        self.assertTrue((result['predicted_mean'] <= result['predicted_upper']).all())

        # Check date format
        self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
        self.assertTrue(all('-' in date_str for date_str in result['index']))

    def test_lightgbm_predict_custom_parameters(self):
        """Test LightGBM prediction with custom parameters"""
        result = LightGBM_predict(
            self.long_df,
            n=3,
            sequence_length=15,
            num_leaves=20,
            learning_rate=0.05,
            feature_fraction=0.8,
            bagging_fraction=0.7,
            num_boost_round=25,
            random_state=123
        )

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 3)
        self.assertFalse(result['predicted_mean'].isna().any())

    def test_lightgbm_predict_invalid_input(self):
        """Test LightGBM prediction with invalid input"""
        # Test with wrong number of columns
        invalid_df = pd.DataFrame({
            'date': ['2023-01-01'],
            'value1': [100],
            'value2': [200]
        })

        result = LightGBM_predict(invalid_df, n=5, num_boost_round=10)
        self.assertIsNone(result)

    def test_lightgbm_predict_insufficient_data(self):
        """Test LightGBM prediction with insufficient data"""
        # Test with data length less than sequence_length + 1
        with self.assertRaises(ValueError):
            LightGBM_predict(self.minimal_df, n=5, sequence_length=10, num_boost_round=10)

    def test_lightgbm_predict_single_prediction(self):
        """Test LightGBM prediction for single future point"""
        result = LightGBM_predict(self.long_df, n=1, sequence_length=10,
                                 num_boost_round=15, num_leaves=10)

        # Check structure
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 1)
        self.assertFalse(result['predicted_mean'].isna().any())

    def test_lightgbm_predict_date_continuity(self):
        """Test that predicted dates are continuous from input data"""
        result = LightGBM_predict(self.long_df, n=3, sequence_length=10,
                                 num_boost_round=15, num_leaves=10)

        # Get last date from input data
        last_input_date = pd.to_datetime(self.long_df['date'].iloc[-1])
        first_predicted_date = pd.to_datetime(result['index'].iloc[0])

        # First predicted date should be one day after last input date
        expected_date = last_input_date + pd.Timedelta(days=1)
        self.assertEqual(first_predicted_date, expected_date)

    def test_lightgbm_predict_reasonable_values(self):
        """Test that LightGBM predictions are in reasonable range"""
        result = LightGBM_predict(self.long_df, n=5, sequence_length=10,
                                 num_boost_round=20, num_leaves=15)

        # Check that predicted values are in reasonable range
        original_min = self.long_df['value'].min()
        original_max = self.long_df['value'].max()
        predicted_values = result['predicted_mean']

        # Predictions should be within a reasonable range (allow some extrapolation)
        # Allow predictions to be within 2x the original range
        range_extension = (original_max - original_min) * 2
        lower_bound = original_min - range_extension
        upper_bound = original_max + range_extension

        self.assertTrue(all(predicted_values >= lower_bound))
        self.assertTrue(all(predicted_values <= upper_bound))

    def test_original_dataframe_not_modified(self):
        """Test that original DataFrame is not modified"""
        original_df = self.long_df.copy()

        LightGBM_predict(self.long_df, n=3, sequence_length=10,
                        num_boost_round=15, num_leaves=10)

        # Check that original DataFrame is unchanged
        pd.testing.assert_frame_equal(self.long_df, original_df)


if __name__ == '__main__':
    unittest.main()
